import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { QRCodeSVG } from 'qrcode.react';
import axios from 'axios';
import socket from '../socket';

function AdminSession() {
  const { code } = useParams();
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchSession();
    
    // Join session room
    socket.emit('join-session', code);
    
    // Listen for real-time updates
    socket.on('session-update', (data) => {
      setSession(data.session);
    });

    return () => {
      socket.off('session-update');
    };
  }, [code]);

  const fetchSession = async () => {
    try {
      const response = await axios.get(`/api/sessions/${code}`);
      setSession(response.data);
    } catch (err) {
      setError('Session not found');
    } finally {
      setLoading(false);
    }
  };

  const toggleResults = async () => {
    try {
      await axios.patch(`/api/sessions/${code}/toggle-results`);
      socket.emit('toggle-results', code);
    } catch (err) {
      setError('Failed to toggle results');
    }
  };

  const endSession = async () => {
    if (window.confirm('Are you sure you want to end this session?')) {
      try {
        await axios.patch(`/api/sessions/${code}/end`);
        setSession(prev => ({ ...prev, isActive: false }));
      } catch (err) {
        setError('Failed to end session');
      }
    }
  };

  const getTotalVotes = () => {
    if (!session) return 0;
    if (session.type === 'poll') {
      return session.options.reduce((total, option) => total + option.voteCount, 0);
    } else {
      return session.options.reduce((total, option) => total + option.selectedCount, 0);
    }
  };

  const getPercentage = (count) => {
    const total = getTotalVotes();
    return total === 0 ? 0 : Math.round((count / total) * 100);
  };

  const joinUrl = `${window.location.origin}/${session?.type}/${code}`;

  if (loading) {
    return <div className="loading">Loading session...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  if (!session) {
    return <div className="error">Session not found</div>;
  }

  return (
    <div className="session-container">
      <div className="session-header">
        <div className="session-code">{code}</div>
        <div className="session-type">
          {session.type === 'poll' ? 'Live Poll' : 'Live Quiz'} - Admin View
        </div>
      </div>

      <div className="admin-controls">
        <h3 className="admin-title">Session Controls</h3>
        <div className="admin-buttons">
          <button 
            className={`admin-btn toggle-results ${session.showResults ? 'showing' : ''}`}
            onClick={toggleResults}
          >
            {session.showResults ? '👁️ Hide Results' : '👁️ Show Results'}
          </button>
          <button 
            className="admin-btn end-session"
            onClick={endSession}
            disabled={!session.isActive}
          >
            🛑 End Session
          </button>
        </div>
      </div>

      <div className="qr-section">
        <h3 className="qr-title">Share with Participants</h3>
        <QRCodeSVG 
          value={joinUrl}
          size={200}
          className="qr-code"
        />
        <div className="join-url">{joinUrl}</div>
      </div>

      <div className="question-card">
        <h2 className="question-text">{session.question}</h2>
      </div>

      <div className="results-card">
        <h3 className="results-title">
          📊 Live Results ({getTotalVotes()} total {session.type === 'poll' ? 'votes' : 'answers'})
        </h3>
        
        {session.options.map((option, index) => {
          const count = session.type === 'poll' ? option.voteCount : option.selectedCount;
          return (
            <div key={index} className="result-item">
              <div className="result-header">
                <div className="result-option">
                  <div className="option-letter">
                    {String.fromCharCode(65 + index)}
                  </div>
                  <span>
                    {option.text}
                    {session.type === 'quiz' && index === session.correctAnswerIndex && ' ✅'}
                  </span>
                </div>
                <span className="result-count">
                  {count} {session.type === 'poll' ? 'votes' : 'answers'}
                </span>
              </div>
              <div className="result-bar">
                <div 
                  className={`result-fill ${
                    session.type === 'quiz' && index === session.correctAnswerIndex ? 'correct' : ''
                  }`}
                  style={{ width: `${getPercentage(count)}%` }}
                >
                  {getPercentage(count)}%
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {session.type === 'quiz' && (
        <div className="results-card">
          <h3 className="results-title">📈 Quiz Analytics</h3>
          <div style={{ display: 'flex', justifyContent: 'space-around', textAlign: 'center' }}>
            <div>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#2ed573' }}>
                {session.options[session.correctAnswerIndex]?.selectedCount || 0}
              </div>
              <div style={{ color: '#666' }}>Correct Answers</div>
            </div>
            <div>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#ff4757' }}>
                {getTotalVotes() - (session.options[session.correctAnswerIndex]?.selectedCount || 0)}
              </div>
              <div style={{ color: '#666' }}>Incorrect Answers</div>
            </div>
            <div>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#667eea' }}>
                {getTotalVotes() > 0 ? 
                  Math.round(((session.options[session.correctAnswerIndex]?.selectedCount || 0) / getTotalVotes()) * 100) : 0
                }%
              </div>
              <div style={{ color: '#666' }}>Success Rate</div>
            </div>
          </div>
        </div>
      )}

      {!session.isActive && (
        <div className="feedback incorrect">
          <div className="feedback-text">
            ⚠️ This session has ended
          </div>
        </div>
      )}
    </div>
  );
}

export default AdminSession;
