{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "remover", "node", "_extra$raw", "extra", "raw", "includes", "replace", "_default", "exports", "default", "declare", "api", "assertVersion", "name", "manipulateOptions", "_", "parser", "plugins", "push", "visitor", "NumericLiteral", "BigIntLiteral"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport type { NodePath, types as t } from \"@babel/core\";\n\n/**\n * Given a bigIntLiteral or NumericLiteral, remove numeric\n * separator `_` from its raw representation\n *\n * @param {NodePath<BigIntLiteral | NumericLiteral>} { node }: A Babel AST node path\n */\nfunction remover({ node }: NodePath<t.BigIntLiteral | t.NumericLiteral>) {\n  const { extra } = node;\n  // @ts-expect-error todo(flow->ts)\n  if (extra?.raw?.includes(\"_\")) {\n    // @ts-expect-error todo(flow->ts)\n    extra.raw = extra.raw.replace(/_/g, \"\");\n  }\n}\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return {\n    name: \"transform-numeric-separator\",\n    manipulateOptions: process.env.BABEL_8_BREAKING\n      ? undefined\n      : (_, parser) => parser.plugins.push(\"numericSeparator\"),\n\n    visitor: {\n      NumericLiteral: remover,\n      BigIntLiteral: remover,\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AASA,SAASC,OAAOA,CAAC;EAAEC;AAAmD,CAAC,EAAE;EAAA,IAAAC,UAAA;EACvE,MAAM;IAAEC;EAAM,CAAC,GAAGF,IAAI;EAEtB,IAAIE,KAAK,aAAAD,UAAA,GAALC,KAAK,CAAEC,GAAG,aAAVF,UAAA,CAAYG,QAAQ,CAAC,GAAG,CAAC,EAAE;IAE7BF,KAAK,CAACC,GAAG,GAAGD,KAAK,CAACC,GAAG,CAACE,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;EACzC;AACF;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,uCAAoB,CAAC;EAEtC,OAAO;IACLC,IAAI,EAAE,6BAA6B;IACnCC,iBAAiB,EAEbA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,kBAAkB,CAAC;IAE1DC,OAAO,EAAE;MACPC,cAAc,EAAEpB,OAAO;MACvBqB,aAAa,EAAErB;IACjB;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}