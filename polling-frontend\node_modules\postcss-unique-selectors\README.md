# [postcss][postcss]-unique-selectors

> Ensure CSS selectors are unique.

## Install

With [npm](https://npmjs.org/package/postcss-unique-selectors) do:

```
npm install postcss-unique-selectors --save
```

## Example

Selectors are sorted naturally, and deduplicated:

### Input

```css
h1,h3,h2,h1 {
    color: red
}
```

### Output

```css
h1,h2,h3 {
    color: red
}
```

## Usage

See the [PostCSS documentation](https://github.com/postcss/postcss#usage) for
examples for your environment.

## Contributors

See [CONTRIBUTORS.md](https://github.com/cssnano/cssnano/blob/master/CONTRIBUTORS.md).

## License

MIT © [Ben <PERSON>](http://beneb.info)

[postcss]: https://github.com/postcss/postcss
