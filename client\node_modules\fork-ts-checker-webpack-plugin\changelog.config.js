module.exports = {
  list: ['feat', 'fix', 'refactor', 'perf', 'test', 'chore', 'docs'],
  maxMessageLength: 64,
  minMessageLength: 3,
  questions: ['type', 'subject', 'body', 'breaking', 'issues'],
  types: {
    feat: {
      description: 'A new feature',
      value: 'feat',
    },
    fix: {
      description: 'A bug fix',
      value: 'fix',
    },
    refactor: {
      description: 'A code change that neither adds a feature or fixes a bug',
      value: 'refactor',
    },
    perf: {
      description: 'A code change that improves performance',
      value: 'perf',
    },
    test: {
      description: 'Adding missing tests',
      value: 'test',
    },
    chore: {
      description: 'Build process, CI or auxiliary tool changes',
      value: 'chore',
    },
    docs: {
      description: 'Documentation only changes',
      value: 'docs',
    },
  },
};
