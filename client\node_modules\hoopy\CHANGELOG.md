# Change log

## 0.1.4

### Other changes

* project: update dev dependencies (e0b36a9)
* project: migrate to gitlab (af3f598)

## 0.1.3

* fix: note minimum required node version (37e4481)
* chore: update authors (cc53233)

## 0.1.2

* chore: add unit test of Array.prototype.slice (557cdb7)
* fix: handle error when attempting to coerce in isInteger (e4e80f9)

## 0.1.1

* chore: trigger ci (b8ca7bd)
* chore: tidy the example code (bb08e45)
* fix: ensure sane grow behaviour for overflowed items (4e47f98)

## 0.1.0

* feature: implement basic circular array type (c17715a)
* fix: throw from shift and unshift (660908f)
* feature: add readme (8928671)
* chore: add change log (cbb2b6b)
* fix: fix lint error (d2b291a)

## 0.0.0

feat: add project boilerplate

