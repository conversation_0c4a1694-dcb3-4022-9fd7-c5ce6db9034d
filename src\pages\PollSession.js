import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import axios from 'axios';
import socket from '../socket';

function PollSession() {
  const { code } = useParams();
  const [session, setSession] = useState(null);
  const [selectedOption, setSelectedOption] = useState(null);
  const [hasVoted, setHasVoted] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchSession();
    
    // Join session room
    socket.emit('join-session', code);
    
    // Listen for real-time updates
    socket.on('session-update', (data) => {
      if (data.type === 'poll') {
        setSession(data.session);
      }
    });

    socket.on('results-visibility-changed', (data) => {
      setSession(data.session);
    });

    socket.on('error', (data) => {
      setError(data.message);
    });

    return () => {
      socket.off('session-update');
      socket.off('results-visibility-changed');
      socket.off('error');
    };
  }, [code]);

  const fetchSession = async () => {
    try {
      const response = await axios.get(`/api/sessions/${code}`);
      const sessionData = response.data;
      
      if (sessionData.type !== 'poll') {
        setError('This is not a poll session');
        return;
      }
      
      setSession(sessionData);
    } catch (err) {
      setError('Poll not found or inactive');
    } finally {
      setLoading(false);
    }
  };

  const handleVote = (optionIndex) => {
    if (hasVoted) return;
    
    setSelectedOption(optionIndex);
    setHasVoted(true);
    
    // Emit vote via socket
    socket.emit('vote', {
      sessionCode: code,
      optionIndex,
      socketId: socket.id
    });
  };

  const getTotalVotes = () => {
    if (!session) return 0;
    return session.options.reduce((total, option) => total + option.voteCount, 0);
  };

  const getPercentage = (voteCount) => {
    const total = getTotalVotes();
    return total === 0 ? 0 : Math.round((voteCount / total) * 100);
  };

  if (loading) {
    return <div className="loading">Loading poll...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  if (!session) {
    return <div className="error">Poll not found</div>;
  }

  return (
    <div className="session-container">
      <div className="session-header">
        <div className="session-code">{code}</div>
        <div className="session-type">Live Poll</div>
      </div>

      <div className="question-card">
        <h2 className="question-text">{session.question}</h2>
        
        {!hasVoted ? (
          <ul className="options-list">
            {session.options.map((option, index) => (
              <li 
                key={index} 
                className="option-item"
                onClick={() => handleVote(index)}
              >
                <div className="option-letter">
                  {String.fromCharCode(65 + index)}
                </div>
                <div className="option-text">{option.text}</div>
              </li>
            ))}
          </ul>
        ) : (
          <div className="feedback correct">
            <div className="feedback-icon">✅</div>
            <div className="feedback-text">
              Thank you for voting! Your response has been recorded.
            </div>
          </div>
        )}
      </div>

      {session.showResults && (
        <div className="results-card">
          <h3 className="results-title">
            📊 Live Results ({getTotalVotes()} total votes)
          </h3>
          
          {session.options.map((option, index) => (
            <div key={index} className="result-item">
              <div className="result-header">
                <div className="result-option">
                  <div className="option-letter">
                    {String.fromCharCode(65 + index)}
                  </div>
                  <span>{option.text}</span>
                </div>
                <span className="result-count">{option.voteCount} votes</span>
              </div>
              <div className="result-bar">
                <div 
                  className={`result-fill ${selectedOption === index ? 'user-selected' : ''}`}
                  style={{ width: `${getPercentage(option.voteCount)}%` }}
                >
                  {getPercentage(option.voteCount)}%
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {!session.showResults && hasVoted && (
        <div className="results-card">
          <h3 className="results-title">⏳ Waiting for results...</h3>
          <p style={{ textAlign: 'center', color: '#666' }}>
            The host will show results when ready
          </p>
        </div>
      )}
    </div>
  );
}

export default PollSession;
