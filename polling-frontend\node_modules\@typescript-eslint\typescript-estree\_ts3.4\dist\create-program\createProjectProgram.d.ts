import { ParseSettings } from '../parseSettings';
import { ASTAndProgram } from './shared';
/**
 * @param parseSettings Internal settings for parsing the file
 * @returns If found, the source file corresponding to the code and the containing program
 */
declare function createProjectProgram(parseSettings: ParseSettings): ASTAndProgram | undefined;
export { createProjectProgram };
//# sourceMappingURL=createProjectProgram.d.ts.map
