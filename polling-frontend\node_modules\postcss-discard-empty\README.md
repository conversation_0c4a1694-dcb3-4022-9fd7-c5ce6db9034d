# [postcss][postcss]-discard-empty

> Discard empty rules and values with PostCSS.

## Install

With [npm](https://npmjs.org/package/postcss-discard-empty) do:

```
npm install postcss-discard-empty --save
```

## Example

For more examples see the [tests](src/__tests__/index.js).

### Input

```css
@font-face;
h1 {}
{color:blue}
h2 {color:}
h3 {color:red}
```

### Output

```css
h3 {color:red}
```

## Usage

See the [PostCSS documentation](https://github.com/postcss/postcss#usage) for
examples for your environment.


## Contributors

See [CONTRIBUTORS.md](https://github.com/cssnano/cssnano/blob/master/CONTRIBUTORS.md).


## License

MIT © [<PERSON>](http://beneb.info)


[postcss]: https://github.com/postcss/postcss
