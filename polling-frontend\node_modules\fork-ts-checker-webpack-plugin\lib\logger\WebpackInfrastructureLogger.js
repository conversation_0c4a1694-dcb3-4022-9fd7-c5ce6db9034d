"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function isInfrastructureLoggerProvider(candidate) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return !!candidate.getInfrastructureLogger;
}
function createWebpackInfrastructure<PERSON>ogger(compiler) {
    return isInfrastructure<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(compiler)
        ? compiler.getInfrastructureLogger('ForkTsCheckerWebpackPlugin')
        : undefined;
}
exports.createWebpackInfrastructureLogger = createWebpackInfrastructureLogger;
