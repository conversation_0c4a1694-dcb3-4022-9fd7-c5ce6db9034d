import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import axios from 'axios';
import socket from '../socket';

function QuizSession() {
  const { code } = useParams();
  const [session, setSession] = useState(null);
  const [selectedOption, setSelectedOption] = useState(null);
  const [hasAnswered, setHasAnswered] = useState(false);
  const [feedback, setFeedback] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchSession();
    
    // Join session room
    socket.emit('join-session', code);
    
    // Listen for real-time updates
    socket.on('session-update', (data) => {
      if (data.type === 'quiz') {
        setSession(data.session);
      }
    });

    socket.on('results-visibility-changed', (data) => {
      setSession(data.session);
    });

    socket.on('answer-feedback', (data) => {
      setFeedback(data);
    });

    socket.on('error', (data) => {
      setError(data.message);
    });

    return () => {
      socket.off('session-update');
      socket.off('results-visibility-changed');
      socket.off('answer-feedback');
      socket.off('error');
    };
  }, [code]);

  const fetchSession = async () => {
    try {
      const response = await axios.get(`/api/sessions/${code}`);
      const sessionData = response.data;
      
      if (sessionData.type !== 'quiz') {
        setError('This is not a quiz session');
        return;
      }
      
      setSession(sessionData);
    } catch (err) {
      setError('Quiz not found or inactive');
    } finally {
      setLoading(false);
    }
  };

  const handleAnswer = (optionIndex) => {
    if (hasAnswered) return;
    
    setSelectedOption(optionIndex);
    setHasAnswered(true);
    
    // Emit answer via socket
    socket.emit('answer', {
      sessionCode: code,
      optionIndex,
      socketId: socket.id
    });
  };

  const getTotalAnswers = () => {
    if (!session) return 0;
    return session.options.reduce((total, option) => total + option.selectedCount, 0);
  };

  const getPercentage = (selectedCount) => {
    const total = getTotalAnswers();
    return total === 0 ? 0 : Math.round((selectedCount / total) * 100);
  };

  if (loading) {
    return <div className="loading">Loading quiz...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  if (!session) {
    return <div className="error">Quiz not found</div>;
  }

  return (
    <div className="session-container">
      <div className="session-header">
        <div className="session-code">{code}</div>
        <div className="session-type">Live Quiz</div>
      </div>

      <div className="question-card">
        <h2 className="question-text">{session.question}</h2>
        
        {!hasAnswered ? (
          <ul className="options-list">
            {session.options.map((option, index) => (
              <li 
                key={index} 
                className="option-item"
                onClick={() => handleAnswer(index)}
              >
                <div className="option-letter">
                  {String.fromCharCode(65 + index)}
                </div>
                <div className="option-text">{option.text}</div>
              </li>
            ))}
          </ul>
        ) : (
          feedback && (
            <div className={`feedback ${feedback.isCorrect ? 'correct' : 'incorrect'}`}>
              <div className="feedback-icon">
                {feedback.isCorrect ? '🎉' : '❌'}
              </div>
              <div className="feedback-text">
                {feedback.isCorrect ? (
                  <strong>Correct!</strong>
                ) : (
                  <>
                    <strong>Incorrect.</strong> The correct answer was: 
                    <strong> {session.options[feedback.correctAnswer]?.text}</strong>
                  </>
                )}
              </div>
            </div>
          )
        )}
      </div>

      {session.showResults && (
        <div className="results-card">
          <h3 className="results-title">
            📊 Live Results ({getTotalAnswers()} total answers)
          </h3>
          
          {session.options.map((option, index) => (
            <div key={index} className="result-item">
              <div className="result-header">
                <div className="result-option">
                  <div className="option-letter">
                    {String.fromCharCode(65 + index)}
                  </div>
                  <span>
                    {option.text}
                    {index === session.correctAnswerIndex && ' ✅'}
                  </span>
                </div>
                <span className="result-count">{option.selectedCount} answers</span>
              </div>
              <div className="result-bar">
                <div 
                  className={`result-fill ${
                    index === session.correctAnswerIndex ? 'correct' : ''
                  } ${selectedOption === index ? 'user-selected' : ''}`}
                  style={{ width: `${getPercentage(option.selectedCount)}%` }}
                >
                  {getPercentage(option.selectedCount)}%
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {!session.showResults && hasAnswered && (
        <div className="results-card">
          <h3 className="results-title">⏳ Waiting for results...</h3>
          <p style={{ textAlign: 'center', color: '#666' }}>
            The host will show results when ready
          </p>
        </div>
      )}
    </div>
  );
}

export default QuizSession;
