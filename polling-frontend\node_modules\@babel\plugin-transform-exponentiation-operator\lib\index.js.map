{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_default", "exports", "default", "declare", "api", "assertVersion", "types", "t", "template", "build", "left", "right", "callExpression", "memberExpression", "identifier", "maybeMemoize", "node", "scope", "isStatic", "assign", "ref", "cloneNode", "path", "isPattern", "id", "generateUidIdentifierBasedOnNode", "push", "assignmentExpression", "name", "visitor", "AssignmentExpression", "operator", "isMemberExpression", "member1", "member2", "object", "replaceWith", "expression", "ast", "property", "computed", "prop", "BinaryExpression"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport type { types as t, Scope } from \"@babel/core\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const { types: t, template } = api;\n\n  function build(left: t.Expression, right: t.Expression) {\n    return t.callExpression(\n      t.memberExpression(t.identifier(\"Math\"), t.identifier(\"pow\")),\n      [left, right],\n    );\n  }\n\n  function maybeMemoize<T extends t.Expression | t.Super>(\n    node: T,\n    scope: Scope,\n  ) {\n    if (scope.isStatic(node)) {\n      return { assign: node, ref: t.cloneNode(node) };\n    }\n\n    if (scope.path.isPattern()) {\n      // We cannot inject a temp var in function arguments.\n      return null;\n    }\n\n    const id = scope.generateUidIdentifierBasedOnNode(node);\n    scope.push({ id });\n    return {\n      assign: t.assignmentExpression(\n        \"=\",\n        t.cloneNode(id),\n        // This is not t.Super, because otherwise the .isStatic check above\n        // would have returned true.\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n        node as t.Expression,\n      ),\n      ref: t.cloneNode(id),\n    };\n  }\n\n  return {\n    name: \"transform-exponentiation-operator\",\n\n    visitor: {\n      AssignmentExpression(path) {\n        const { node, scope } = path;\n        if (node.operator !== \"**=\") return;\n\n        if (t.isMemberExpression(node.left)) {\n          let member1: t.Expression;\n          let member2: t.Expression;\n\n          const object = maybeMemoize(node.left.object, scope);\n          if (!object) {\n            // We need to inject a temp var, but we are in function parameters\n            // and thus cannot. Wrap the expression in an IIFE. It will be\n            // eventually requeued and transformed.\n            path.replaceWith(template.expression.ast`(() => ${path.node})()`);\n            return;\n          }\n\n          const { property, computed } = node.left;\n\n          if (computed) {\n            const prop = maybeMemoize(property as t.Expression, scope);\n            member1 = t.memberExpression(object.assign, prop.assign, true);\n            member2 = t.memberExpression(object.ref, prop.ref, true);\n          } else {\n            member1 = t.memberExpression(object.assign, property, false);\n            member2 = t.memberExpression(\n              object.ref,\n              t.cloneNode(property),\n              false,\n            );\n          }\n\n          path.replaceWith(\n            t.assignmentExpression(\"=\", member1, build(member2, node.right)),\n          );\n        } else {\n          path.replaceWith(\n            t.assignmentExpression(\n              \"=\",\n              node.left,\n              build(\n                // todo: it could be a t.AsExpression\n                t.cloneNode(node.left) as t.Identifier,\n                node.right,\n              ),\n            ),\n          );\n        }\n      },\n\n      BinaryExpression(path) {\n        const { node } = path;\n        if (node.operator === \"**\") {\n          path.replaceWith(\n            build(\n              // left can be PrivateName only if operator is `\"in\"`\n              node.left as t.Expression,\n              node.right,\n            ),\n          );\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAAqD,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAGtC,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,uCAAoB,CAAC;EAEtC,MAAM;IAAEC,KAAK,EAAEC,CAAC;IAAEC;EAAS,CAAC,GAAGJ,GAAG;EAElC,SAASK,KAAKA,CAACC,IAAkB,EAAEC,KAAmB,EAAE;IACtD,OAAOJ,CAAC,CAACK,cAAc,CACrBL,CAAC,CAACM,gBAAgB,CAACN,CAAC,CAACO,UAAU,CAAC,MAAM,CAAC,EAAEP,CAAC,CAACO,UAAU,CAAC,KAAK,CAAC,CAAC,EAC7D,CAACJ,IAAI,EAAEC,KAAK,CACd,CAAC;EACH;EAEA,SAASI,YAAYA,CACnBC,IAAO,EACPC,KAAY,EACZ;IACA,IAAIA,KAAK,CAACC,QAAQ,CAACF,IAAI,CAAC,EAAE;MACxB,OAAO;QAAEG,MAAM,EAAEH,IAAI;QAAEI,GAAG,EAAEb,CAAC,CAACc,SAAS,CAACL,IAAI;MAAE,CAAC;IACjD;IAEA,IAAIC,KAAK,CAACK,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE;MAE1B,OAAO,IAAI;IACb;IAEA,MAAMC,EAAE,GAAGP,KAAK,CAACQ,gCAAgC,CAACT,IAAI,CAAC;IACvDC,KAAK,CAACS,IAAI,CAAC;MAAEF;IAAG,CAAC,CAAC;IAClB,OAAO;MACLL,MAAM,EAAEZ,CAAC,CAACoB,oBAAoB,CAC5B,GAAG,EACHpB,CAAC,CAACc,SAAS,CAACG,EAAE,CAAC,EAIfR,IACF,CAAC;MACDI,GAAG,EAAEb,CAAC,CAACc,SAAS,CAACG,EAAE;IACrB,CAAC;EACH;EAEA,OAAO;IACLI,IAAI,EAAE,mCAAmC;IAEzCC,OAAO,EAAE;MACPC,oBAAoBA,CAACR,IAAI,EAAE;QACzB,MAAM;UAAEN,IAAI;UAAEC;QAAM,CAAC,GAAGK,IAAI;QAC5B,IAAIN,IAAI,CAACe,QAAQ,KAAK,KAAK,EAAE;QAE7B,IAAIxB,CAAC,CAACyB,kBAAkB,CAAChB,IAAI,CAACN,IAAI,CAAC,EAAE;UACnC,IAAIuB,OAAqB;UACzB,IAAIC,OAAqB;UAEzB,MAAMC,MAAM,GAAGpB,YAAY,CAACC,IAAI,CAACN,IAAI,CAACyB,MAAM,EAAElB,KAAK,CAAC;UACpD,IAAI,CAACkB,MAAM,EAAE;YAIXb,IAAI,CAACc,WAAW,CAAC5B,QAAQ,CAAC6B,UAAU,CAACC,GAAG,UAAUhB,IAAI,CAACN,IAAI,KAAK,CAAC;YACjE;UACF;UAEA,MAAM;YAAEuB,QAAQ;YAAEC;UAAS,CAAC,GAAGxB,IAAI,CAACN,IAAI;UAExC,IAAI8B,QAAQ,EAAE;YACZ,MAAMC,IAAI,GAAG1B,YAAY,CAACwB,QAAQ,EAAkBtB,KAAK,CAAC;YAC1DgB,OAAO,GAAG1B,CAAC,CAACM,gBAAgB,CAACsB,MAAM,CAAChB,MAAM,EAAEsB,IAAI,CAACtB,MAAM,EAAE,IAAI,CAAC;YAC9De,OAAO,GAAG3B,CAAC,CAACM,gBAAgB,CAACsB,MAAM,CAACf,GAAG,EAAEqB,IAAI,CAACrB,GAAG,EAAE,IAAI,CAAC;UAC1D,CAAC,MAAM;YACLa,OAAO,GAAG1B,CAAC,CAACM,gBAAgB,CAACsB,MAAM,CAAChB,MAAM,EAAEoB,QAAQ,EAAE,KAAK,CAAC;YAC5DL,OAAO,GAAG3B,CAAC,CAACM,gBAAgB,CAC1BsB,MAAM,CAACf,GAAG,EACVb,CAAC,CAACc,SAAS,CAACkB,QAAQ,CAAC,EACrB,KACF,CAAC;UACH;UAEAjB,IAAI,CAACc,WAAW,CACd7B,CAAC,CAACoB,oBAAoB,CAAC,GAAG,EAAEM,OAAO,EAAExB,KAAK,CAACyB,OAAO,EAAElB,IAAI,CAACL,KAAK,CAAC,CACjE,CAAC;QACH,CAAC,MAAM;UACLW,IAAI,CAACc,WAAW,CACd7B,CAAC,CAACoB,oBAAoB,CACpB,GAAG,EACHX,IAAI,CAACN,IAAI,EACTD,KAAK,CAEHF,CAAC,CAACc,SAAS,CAACL,IAAI,CAACN,IAAI,CAAC,EACtBM,IAAI,CAACL,KACP,CACF,CACF,CAAC;QACH;MACF,CAAC;MAED+B,gBAAgBA,CAACpB,IAAI,EAAE;QACrB,MAAM;UAAEN;QAAK,CAAC,GAAGM,IAAI;QACrB,IAAIN,IAAI,CAACe,QAAQ,KAAK,IAAI,EAAE;UAC1BT,IAAI,CAACc,WAAW,CACd3B,KAAK,CAEHO,IAAI,CAACN,IAAI,EACTM,IAAI,CAACL,KACP,CACF,CAAC;QACH;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}