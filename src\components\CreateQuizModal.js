import React, { useState } from 'react';
import axios from 'axios';

function CreateQuizModal({ onClose, onQuizCreated }) {
  const [question, setQuestion] = useState('');
  const [options, setOptions] = useState(['', '']);
  const [correctAnswerIndex, setCorrectAnswerIndex] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleOptionChange = (index, value) => {
    const newOptions = [...options];
    newOptions[index] = value;
    setOptions(newOptions);
  };

  const addOption = () => {
    if (options.length < 5) {
      setOptions([...options, '']);
    }
  };

  const removeOption = (index) => {
    if (options.length > 2) {
      const newOptions = options.filter((_, i) => i !== index);
      setOptions(newOptions);
      
      // Adjust correct answer index if necessary
      if (correctAnswerIndex >= newOptions.length) {
        setCorrectAnswerIndex(newOptions.length - 1);
      } else if (correctAnswerIndex > index) {
        setCorrectAnswerIndex(correctAnswerIndex - 1);
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    
    // Validation
    if (!question.trim()) {
      setError('Question is required');
      return;
    }

    const validOptions = options.filter(option => option.trim() !== '');
    if (validOptions.length < 2) {
      setError('At least 2 options are required');
      return;
    }

    if (correctAnswerIndex >= validOptions.length) {
      setError('Please select a valid correct answer');
      return;
    }

    setLoading(true);
    
    try {
      const response = await axios.post('/api/sessions/quiz', {
        question: question.trim(),
        options: validOptions,
        correctAnswerIndex
      });
      
      onQuizCreated(response.data);
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to create quiz');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2 className="modal-title">🧠 Create Live Quiz</h2>
          <p className="modal-subtitle">Create an interactive quiz with correct answers</p>
        </div>

        {error && <div className="error">{error}</div>}

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label className="form-label">Quiz Question</label>
            <textarea
              className="form-textarea"
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
              placeholder="What would you like to ask your audience?"
              required
            />
          </div>

          <div className="form-group">
            <label className="form-label">Answer Options</label>
            <div className="options-container">
              {options.map((option, index) => (
                <div key={index} className="option-input-group">
                  <input
                    type="text"
                    className="option-input"
                    value={option}
                    onChange={(e) => handleOptionChange(index, e.target.value)}
                    placeholder={`Option ${index + 1}`}
                    required
                  />
                  {options.length > 2 && (
                    <button
                      type="button"
                      className="remove-option-btn"
                      onClick={() => removeOption(index)}
                    >
                      ✕
                    </button>
                  )}
                </div>
              ))}
              
              {options.length < 5 && (
                <button
                  type="button"
                  className="add-option-btn"
                  onClick={addOption}
                >
                  + Add Option
                </button>
              )}
            </div>
          </div>

          <div className="form-group">
            <label className="form-label">Correct Answer</label>
            <select
              className="correct-answer-select"
              value={correctAnswerIndex}
              onChange={(e) => setCorrectAnswerIndex(parseInt(e.target.value))}
            >
              {options.map((option, index) => (
                <option key={index} value={index}>
                  Option {index + 1}: {option || '(empty)'}
                </option>
              ))}
            </select>
          </div>

          <div className="modal-buttons">
            <button 
              type="button" 
              className="btn btn-secondary"
              onClick={onClose}
            >
              Cancel
            </button>
            <button 
              type="submit" 
              className="btn btn-primary"
              disabled={loading}
            >
              {loading ? 'Creating...' : 'Create Quiz'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default CreateQuizModal;
